import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../common/config.dart';
import '../../../../common/config/models/cart_config.dart';
import '../../../../common/tools.dart';
import '../../../../data/boxes.dart';
import '../../../../generated/l10n.dart';
import '../../../../models/app_model.dart';
import '../../../../models/extra_setting_model.dart';
import '../../../../screens/products/colors/choose_color_card.dart';
import '../../../../screens/products/sizes/choose_size_card.dart';
import '../../../../services/index.dart';
import '../../quantity_selection/quantity_selection.dart';
import '../cart_item_state_ui.dart';

final sizeRowCartItem = [
  null,
  100.0,
  180.0,
  100.0,
  100.0,
];

class CartItemWebWidget extends StatelessWidget {
  const CartItemWebWidget(
    this.stateUI, {
    super.key,
    required this.setState,
    required this.fromCheckout,
  });

  final CartItemStateUI stateUI;
  final Function setState;
  final bool fromCheckout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final selectedProducts =
        UserBox().selectedSizeAndColor[stateUI.product.id.toString()];

    final selectedSizeAndColorIsEmpty =
        selectedProducts == null || selectedProducts.isEmpty;

    //TODO-SizePriceWeb
    final totalProductQuantity = selectedProducts != null
        ? selectedProducts.fold<int>(
            0,
            (previousValue, element) => previousValue + (element.quantity ?? 0),
          )
        : 0;

    final currency = Provider.of<AppModel>(context).currency;
    final currencyRate = Provider.of<AppModel>(context).currencyRate;

    final infosWidget = [
      if (stateUI.product.options != null &&
          stateUI.cartItemMetaData?.options != null)
        Services().widget.renderOptionsCartItem(
            stateUI.product, stateUI.cartItemMetaData?.options),
      if (stateUI.cartItemMetaData?.variation != null)
        Services().widget.renderVariantCartItem(
              context,
              stateUI.cartItemMetaData!.variation!,
              stateUI.cartItemMetaData?.options,
              style: AttributeProductCartStyle.column,
            ),
      if (stateUI.cartItemMetaData?.addonsOptions?.isNotEmpty ?? false)
        Services().widget.renderAddonsOptionsCartItem(
            context, stateUI.cartItemMetaData?.addonsOptions),
      if (selectedProducts != null && selectedProducts.isNotEmpty)
        Wrap(
          children: selectedProducts.map((selectedProduct) {
            return Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  if (kProductDetail.showStockQuantity)
                    QuantitySelection(
                      enabled: (stateUI.inStock || stateUI.isOnBackorder) &&
                          stateUI.onChangeQuantity != null,

                      width: 60,
                      height: 32,
                      color: Theme.of(context).colorScheme.secondary,
                      limitSelectQuantity: stateUI.isOnBackorder
                          ? kCartDetail['maxAllowQuantity']
                          : stateUI.limitQuantity,
                      value: selectedProduct.quantity,
                      onChanged: fromCheckout
                          ? null
                          : (newQuantity) {
                              // Get the current selectedSizeAndColor map
                              var currentMap = UserBox().selectedSizeAndColor;

                              // Find the corresponding SelectedSizeAndColor list
                              var selectedSizeAndColorList =
                                  currentMap[stateUI.product.id.toString()];

                              if (selectedSizeAndColorList != null) {
                                // Create a new list that replaces the item with updated quantity
                                var newList =
                                    selectedSizeAndColorList.map((item) {
                                  if (item.size == selectedProduct.size &&
                                      item.color == selectedProduct.color) {
                                    // Create a new SelectedSizeAndColor object with updated quantity
                                    return SelectedSizeAndColor(
                                      size: item.size,
                                      color: item.color,
                                      quantity: newQuantity,
                                      price: item.price,
                                    );
                                  } else {
                                    return item;
                                  }
                                }).toList();

                                // Update the map with the new list
                                currentMap[stateUI.product.id.toString()] =
                                    newList;

                                // Update the map in the UserBox
                                UserBox().selectedSizeAndColor = currentMap;

                                setState(() {});
                              }

                              // get old quantity for other colors
                              var totalOtherProductQuantities = 0;

                              for (var item in selectedSizeAndColorList!) {
                                totalOtherProductQuantities +=
                                    item.quantity ?? 1;
                              }

                              // subtract the old quantity of the selected color
                              totalOtherProductQuantities -=
                                  selectedProduct.quantity ?? 1;

                              // add the new quantity of the selected color
                              final allQuantities =
                                  totalOtherProductQuantities + newQuantity;

                              log('OLD_QUANTITY: $totalOtherProductQuantities + $newQuantity = $allQuantities');

                              return stateUI.onChangeQuantity
                                      ?.call(allQuantities) ??
                                  true;
                            },
                      // onChanged: (newQuantity) {
                      //   // Get the current selectedSizeAndColor map
                      //   var currentMap = UserBox().selectedSizeAndColor;
                      //
                      //   // Find the corresponding SelectedSizeAndColor list
                      //   var selectedSizeAndColorList =
                      //       currentMap[stateUI.product.id.toString()];
                      //
                      //   if (selectedSizeAndColorList != null) {
                      //     // Create a new list that replaces the item with updated quantity
                      //     var newList =
                      //         selectedSizeAndColorList.map((item) {
                      //       if (item.size == selectedProduct.size &&
                      //           item.color == selectedProduct.color) {
                      //         // Create a new SelectedSizeAndColor object with updated quantity
                      //         return SelectedSizeAndColor(
                      //           size: item.size,
                      //           color: item.color,
                      //           quantity: newQuantity,
                      //         );
                      //       } else {
                      //         return item;
                      //       }
                      //     }).toList();
                      //
                      //     // Update the map with the new list
                      //     currentMap[stateUI.product.id.toString()] =
                      //         newList;
                      //
                      //     // Update the map in the UserBox
                      //     UserBox().selectedSizeAndColor = currentMap;
                      //
                      //     setState(() {});
                      //   }
                      //
                      //   return stateUI.onChangeQuantity
                      //           ?.call(newQuantity) ??
                      //       true;
                      // },
                      // useNewDesign: false,
                    ),
                  const SizedBox(
                    width: 10,
                  ),
                  if (selectedProduct.size?.isNotEmpty ?? false) ...[
                    const SizedBox(
                      width: 10,
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: ChooseSizeCard(
                        size: selectedProduct.size ?? '',
                        isSelected: true,
                        fromCart: true,
                      ),
                    ),
                  ],
                  if (selectedProduct.color?.isNotEmpty ?? false) ...[
                    const SizedBox(
                      width: 10,
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: ChooseColorCard(
                        color: selectedProduct.color ?? '',
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
        ),
      if (stateUI.product.store != null &&
          (stateUI.product.store?.name != null &&
              stateUI.product.store!.name!.trim().isNotEmpty))
        const SizedBox(height: 10),
      if (!stateUI.inStock || stateUI.isOnBackorder) const SizedBox(height: 5),
      if (stateUI.isOnBackorder)
        Text(S.of(context).backOrder,
            style: TextStyle(
              color: kStockColor.backorder,
            )),
      if (!stateUI.isOnBackorder && !stateUI.inStock)
        Text(
          S.of(context).outOfStock,
          style: const TextStyle(color: Colors.red),
        ),
      if (!stateUI.isOnBackorder &&
          stateUI.inStock &&
          stateUI.quantity != null &&
          stateUI.quantity! > stateUI.limitQuantity)
        Text(
          S.of(context).quantityProductExceedInStock,
          style: const TextStyle(color: Colors.red),
        ),
      Row(
        children: [
          if (stateUI.showStoreName &&
              (stateUI.product.store?.name?.isNotEmpty ?? false))
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: Text(
                  stateUI.product.store?.name ?? '',
                  style: TextStyle(
                      color: theme.colorScheme.secondary, fontSize: 12),
                ),
              ),
            )
        ],
      ),
    ];

    final items = [
      Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80,
            height: 80,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: ImageResize(
                url: stateUI.imageFeature,
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  stateUI.product.name ?? '',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                ...infosWidget
              ],
            ),
          ),
        ],
      ),
      if (selectedProducts?.lastOrNull?.price == null)
        Align(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            stateUI.price!,
            style: TextStyle(
              color: theme.colorScheme.secondary,
              fontSize: 13,
            ),
          ),
        )
      else
        Align(
          alignment: AlignmentDirectional.centerStart,
          child: Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Column(
              spacing: 30,
              children: selectedProducts?.map(
                    (selectedProduct) {
                      return Text(
                        PriceTools.getCurrencyFormatted(
                                selectedProduct.price, currencyRate,
                                currency: currency) ??
                            '',
                        style: TextStyle(
                          color: theme.colorScheme.secondary,
                          fontSize: 13,
                        ),
                      );
                    },
                  ).toList() ??
                  [],
            ),
          ),
        ),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        child: Align(
          alignment: AlignmentDirectional.centerStart,
          child: SizedBox(
            width: 127,
            height: 40,
            child: stateUI.enabledTextBoxQuantity && selectedSizeAndColorIsEmpty
                ? QuantitySelection(
                    enabled: (stateUI.inStock || stateUI.isOnBackorder) &&
                        stateUI.onChangeQuantity != null,
                    height: 40,
                    width: 45,
                    quantityStep: stateUI.product.quantityStep,
                    enabledTextBox: stateUI.enabledTextBoxQuantity,
                    color: Theme.of(context).colorScheme.secondary,
                    limitSelectQuantity: stateUI.isOnBackorder
                        ? kCartDetail['maxAllowQuantity']
                        : stateUI.limitQuantity,
                    value: stateUI.quantity,
                    onChanged: (value) {
                      return stateUI.onChangeQuantity
                              ?.call(value == -1 ? 1 : value) ??
                          true;
                    },
                    style: QuantitySelectionStyle.style03,
                  )
                : Align(
                    alignment: AlignmentDirectional.centerStart,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Text(
                        totalProductQuantity.toString(),
                        // stateUI.quantity?.toString() ?? '1',
                        style: TextStyle(
                          color: theme.colorScheme.secondary,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ),
          ),
        ),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            stateUI.priceWithQuantity!,
            style: TextStyle(
              color: theme.colorScheme.secondary,
              fontSize: 13,
            ),
          ),
          // Show quantity discount indicator
          if (_hasQuantityDiscount())
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.local_offer,
                      color: Colors.green,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getDiscountText(context),
                      style: const TextStyle(
                        color: Colors.green,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      if (stateUI.onRemove != null && stateUI.enabledTextBoxQuantity)
        GestureDetector(
          onTap: stateUI.onRemove,
          behavior: HitTestBehavior.translucent,
          child: Container(
            height: 35,
            padding: const EdgeInsets.symmetric(
              vertical: 5,
              horizontal: 0,
            ).copyWith(left: 10),
            child: const Align(
              alignment: Alignment.topCenter,
              child: Icon(
                CupertinoIcons.delete,
                size: 18,
              ),
            ),
          ),
        ),
    ];

    return GestureDetector(
      key: ValueKey(stateUI.product.id),
      onTap: () => stateUI.onTapProduct(context, product: stateUI.product),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: List.generate(
              items.length,
              (index) {
                final item = items[index];
                final size = index > (sizeRowCartItem.length - 1)
                    ? null
                    : sizeRowCartItem[index];
                if (size == null) {
                  return Expanded(child: item);
                }
                return SizedBox(width: size, child: item);
              },
            ),
          ),
        ),
      ),
    );
  }

  bool _hasQuantityDiscount() {
    final selectedProducts =
        UserBox().selectedSizeAndColor[stateUI.product.id.toString()];

    if (selectedProducts == null || selectedProducts.isEmpty) {
      return false;
    }

    final totalQuantity = selectedProducts.fold<int>(
      0,
      (previousValue, element) => previousValue + (element.quantity ?? 0),
    );

    return stateUI.product.minQuantitySaleNumber != null &&
        stateUI.product.quantitySale != null &&
        stateUI.product.quantitySale! > 0 &&
        totalQuantity >= (stateUI.product.minQuantitySaleNumber ?? 0);
  }

  String _getDiscountText(BuildContext context) {
    if (stateUI.product.isQuantitySalePercentage == true) {
      return '${stateUI.product.quantitySale}% ${S.of(context).off}';
    } else {
      return S.of(context).discountApplied;
    }
  }
}
