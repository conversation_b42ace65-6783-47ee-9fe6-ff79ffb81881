import 'dart:async';

import 'package:flutter/material.dart';
import 'package:inspireui/widgets/coupon_card.dart' show Coupon;
import 'package:provider/provider.dart';

import '../../common/config.dart';
import '../../common/config/models/cart_config.dart';
import '../../common/constants.dart';
import '../../common/tools.dart';
import '../../data/boxes.dart';
import '../../generated/l10n.dart';
import '../../models/cart/cart_item_meta_data.dart';
import '../../models/entities/filter_sorty_by.dart';
import '../../models/index.dart';
import '../../services/index.dart';
import '../frameworks.dart';
import '../product_variant_mixin.dart';
import 'strapi_variant_mixin.dart';

class StrapiWidget extends BaseFrameworks
    with ProductVariantMixin, StrapiVariantMixin {
  static final StrapiWidget _instance = StrapiWidget._internal();

  factory StrapiWidget() => _instance;

  StrapiWidget._internal();

  Map<String, dynamic>? configCache;

  @override
  bool get enableProductReview => true;

  bool checkValidCoupon(context, Coupon coupon, String couponCode) {
    final totalCart =
        Provider.of<CartModel>(context, listen: false).getSubTotal()!;

    if ((coupon.minimumAmount > totalCart && coupon.minimumAmount != 0.0) ||
        (coupon.maximumAmount < totalCart && coupon.maximumAmount != 0.0)) {
      return false;
    }

    if (coupon.dateExpires != null &&
        coupon.dateExpires!.isBefore(DateTime.now())) {
      return false;
    }

    return coupon.code == couponCode;
  }

  @override
  Future<void> applyCoupon(context,
      {Coupons? coupons,
      String? code,
      Function? success,
      Function? error}) async {
    var isExisted = false;
    for (var coupon in coupons!.coupons) {
      if (checkValidCoupon(context, coupon, code!.toLowerCase())) {
        success!(coupon);
        isExisted = true;
        break;
      }
    }
    if (!isExisted) {
      error!(S.of(context).couponInvalid);
    }
  }

  @override
  Future<void> doCheckout(context,
      {Function? success, Function? error, Function? loading}) async {
    final cartModel = Provider.of<CartModel>(context, listen: false);
    final userModel = Provider.of<UserModel>(context, listen: false);

    if (kPaymentConfig.enableOnePageCheckout) {
      var params = Order().toJson(cartModel, userModel.user?.id, true);
      params['token'] = userModel.user?.cookie;

      return;
    }

    /// return success to navigate to Native payment
    success!();
  }

  @override
  Future<void> createOrder(
    context, {
    Function? onLoading,
    Function? success,
    Function? error,
    paid = false,
    cod = false,
    bacs = false,
    AdditionalPaymentInfo? additionalPaymentInfo,
  }) async {
    var listOrder = <Map>[];
    var isLoggedIn = Provider.of<UserModel>(context, listen: false).loggedIn;
    final cartModel = Provider.of<CartModel>(context, listen: false);
    final userModel = Provider.of<UserModel>(context, listen: false);

    try {
      final order = await Services().api.createOrder(context,
          cartModel: cartModel, user: userModel, paid: paid)!;

      final totalPrice = order.total;

      final isDefaultLangAr = currentVendor?.config?.defaultLanguage == 'ar';

      await Services().firebase.sendNotification(
          title: isDefaultLangAr ? 'لديك طلب جديد !' : 'You Have New Order !',
          body: isDefaultLangAr
              ? '${order.totalProducts}عدد المنتجات: ' +
                  '\n' +
                  'الإجمالي: ${totalPrice.toString()}'
              : 'Total Products: ${order.totalProducts}\nTotal Price: ${totalPrice.toString()}',
          userToken: vendorBusinessName.toString(),
          isTopic: true);
      // await Services().firebase.sendNotification(
      //       title: S.of(context).youHaveNewOrder,
      //       body:
      //           '${S.of(context).totalProductItems(order.totalProducts)}\n${S.of(context).totalPriceNumber(order.total.toString())}',
      //       userToken: vendorBusinessName.toString(),
      //       isTopic: true,
      //     );

      if (!isLoggedIn) {
        var items = UserBox().orders;
        if (items.isNotEmpty) {
          listOrder = items;
        }
        listOrder.add(order.toOrderJson(cartModel, null));
        UserBox().orders = listOrder;
      }
      success!(order);
    } catch (e) {
      error!(e.toString());
    }
  }

  @override
  void placeOrder(context,
      {CartModel? cartModel,
      PaymentMethod? paymentMethod,
      Function? onLoading,
      Function? success,
      Function? error}) {
    createOrder(
      context,
      onLoading: onLoading,
      success: success,
      error: error,
    );
  }

  @override
  Map<String, dynamic>? getPaymentUrl(context) {
    return null;
  }

  @override
  void updateUserInfo({
    User? loggedInUser,
    context,
    required onError,
    onSuccess,
    required currentPassword,
    required userDisplayName,
    userEmail,
    userNiceName,
    userUrl,
    userPassword,
    userFirstname,
    userLastname,
    userPhone,
  }) {
    var params = {
      'user_id': loggedInUser!.id,
      'email': userEmail,
      'username': userEmail,
      if (userFirstname != null && userFirstname.isNotEmpty)
        'firstName': userFirstname,
      if (userLastname != null && userLastname.isNotEmpty)
        'lastName': userLastname,
      'displayName': userDisplayName,
      if (userPhone != null && userPhone.isNotEmpty) 'phone': userPhone,
      if (userPassword != null && userPassword.isNotEmpty)
        'password': userPassword,
    };
    if (!loggedInUser.isSocial! && userPassword!.isNotEmpty) {
      params['user_pass'] = userPassword;
    }
    if (!loggedInUser.isSocial! && currentPassword.isNotEmpty) {
      params['current_pass'] = currentPassword;
    }
    Services().api.updateUserInfo(params, loggedInUser.cookie)?.then((value) {
      var param = value ?? {};

      param['password'] = userPassword;

      if (onSuccess != null) {
        final user = User.fromStrapi(param);

        printLog('ads22222assff $param\n YYYY $user');

        UserBox().userInfo = user;

        onSuccess(user);
      }
      // onSuccess!(User.fromStrapi(param));
    }).catchError((e) {
      onError(e.toString());
    });
  }

  @override
  Future<void> onLoadedAppConfig(String? lang, Function callback) async {
    if (kAdvanceConfig.isCaching) {
      final configCache = await Services().api.getHomeCache(lang);
      if (configCache != null) {
        callback(configCache);
      }
    }
  }

  @override
  Widget renderVariantCartItem(
    BuildContext context,
    variation,
    Map? options, {
    AttributeProductCartStyle style = AttributeProductCartStyle.normal,
  }) {
    var list = <Widget>[];
    for (var att in variation.attributes) {
      list.add(Row(
        children: <Widget>[
          ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 50.0, maxWidth: 200),
            child: Text(
              '${att.name![0].toUpperCase()}${att.name!.substring(1)} ',
            ),
          ),
          att.name == 'color'
              ? Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Container(
                      width: 15,
                      height: 15,
                      decoration: BoxDecoration(
                        color: HexColor(
                          kNameToHex[att.option!.toLowerCase()]!,
                        ),
                      ),
                    ),
                  ),
                )
              : Expanded(
                  child: Text(
                  att.option!,
                  textAlign: TextAlign.end,
                )),
        ],
      ));
      list.add(const SizedBox(
        height: 5.0,
      ));
    }

    return Column(children: list);
  }

  @override
  void loadShippingMethods(context, CartModel cartModel, bool beforehand) {
//    if (!beforehand) return;
//    final cartModel = Provider.of<CartModel>(context, listen: false);
    Future.delayed(Duration.zero, () {
      final token = context.read<UserModel>().user?.cookie;
      var langCode = Provider.of<AppModel>(context, listen: false).langCode;
      Provider.of<ShippingMethodModel>(context, listen: false)
          .getShippingMethods(
              cartModel: cartModel, token: token, langCode: langCode);
    });
  }

  @override
  Widget renderButtons(
      BuildContext context, Order order, cancelOrder, createRefund) {
    return Row(
      children: <Widget>[
        Expanded(
          child: Center(
            child: GestureDetector(
              onTap: cancelOrder,
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: (order.status?.isCanceled ?? false)
                        ? Colors.blueGrey
                        : Colors.red),
                child: Text(
                  S.of(context).cancel.toUpperCase(),
                  // 'Cancel'.toUpperCase(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        // Expanded(
        //   child: Center(
        //     child: GestureDetector(
        //       onTap: createRefund,
        //       child: Container(
        //         padding: const EdgeInsets.all(10),
        //         decoration: BoxDecoration(
        //             borderRadius: BorderRadius.circular(10),
        //             color: order.status == OrderStatus.refunded
        //                 ? Colors.blueGrey
        //                 : Colors.lightBlue),
        //         child: Text(
        //           'Refunds'.toUpperCase(),
        //           style: const TextStyle(color: Colors.white),
        //         ),
        //       ),
        //     ),
        //   ),
        // )
      ],
    );
  }

  @override
  String? getPriceItemInCart(
    Product product,
    CartItemMetaData? cartItemMetaData,
    Map<String, dynamic> currencyRate,
    String? currency, {
    int quantity = 1,
  }) {
    if (cartItemMetaData?.variation != null &&
        cartItemMetaData?.variation?.id != null) {
      return PriceTools.getVariantPriceProductValue(
        cartItemMetaData?.variation,
        currencyRate,
        currency,
        onSale: true,
        selectedOptions: cartItemMetaData?.addonsOptions,
        quantity: quantity,
      );
    } else {
      // Use effective price (sale price if available, otherwise regular price)
      final effectivePrice = PriceTools.getEffectivePrice(product);

      if (effectivePrice <= 0) {
        return '';
      }

      // Apply quantity discount to effective price
      final discountedPrice = PriceTools.calculateQuantityDiscountPrice(
        basePrice: effectivePrice,
        quantity: quantity,
        minQuantitySaleNumber: product.minQuantitySaleNumber,
        quantitySale: product.quantitySale,
        isQuantitySalePercentage: product.isQuantitySalePercentage,
      );

      return PriceTools.getCurrencyFormatted(discountedPrice, currencyRate,
          currency: currency);
    }
  }

  @override
  Future<List<Country>> loadCountries() async {
    var countries = <Country>[];
    if (kDefaultCountry.isNotEmpty) {
      for (var item in kDefaultCountry) {
        countries.add(Country.fromConfig(
            item['iosCode'], item['name'], item['icon'], []));
      }
    }
    return countries;
  }

  @override
  Future<List<CountryState>> loadStates(Country country) async {
    final items = await Tools.loadStatesByCountry(country.id!);
    var states = <CountryState>[];
    if (items.isNotEmpty) {
      for (var item in items) {
        states.add(CountryState.fromConfig(item));
      }
    }
    return states;
  }

  @override
  Future<void> resetPassword(BuildContext context, String username) async {
    final forgotPasswordUrl = ServerConfig().forgetPassword ??
        '${ServerConfig().url}/wp-login.php?action=lostpassword';

    var data = <String, dynamic>{'user_login': username};
    try {
      final val = await (Provider.of<UserModel>(context, listen: false)
              .submitForgotPassword(forgotPwLink: forgotPasswordUrl, data: data)
          as Future<String>);

      Tools.showSnackBar(ScaffoldMessenger.of(context), val);

      if (val == 'Check your email for confirmation link') {
        Future.delayed(
            const Duration(seconds: 1), () => Navigator.of(context).pop());
      }
      return;
    } catch (e) {
      rethrow;
    }
  }

  @override
  List<OrderByType> get supportedSortByOptions => [
        OrderByType.title,
        OrderByType.rating,
        OrderByType.date,
        OrderByType.price
      ];
}
