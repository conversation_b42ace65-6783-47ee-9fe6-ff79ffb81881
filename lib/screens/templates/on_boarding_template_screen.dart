import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:fstore/common/constants/local_keys.dart';
import 'package:fstore/frameworks/strapi/services/strapi_service.dart';
import 'package:fstore/models/template_model.dart';
import 'package:fstore/screens/checkout/widgets/convet_city_lang.dart';
import 'package:fstore/widgets/animation/animated_text.dart';
import 'package:fstore/widgets/common/flux_image.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:universal_html/html.dart' as html;

import '../../common/config.dart';
import '../../common/constants.dart';
import '../../common/extensions/animation_extensions.dart';
import '../../context_extensions.dart';
import '../../data/boxes.dart';
import '../../models/cart/cart_model.dart';
import '../../models/product_wish_list_model.dart';
import '../../models/shipping_method_model.dart';
import '../../modules/dynamic_layout/banner/banner_slider.dart';
import '../../modules/dynamic_layout/helper/helper.dart';
import '../../modules/dynamic_layout/product/future_builder.dart';
import '../../services/get_storage_service.dart';
import '../../services/http_cache_manager.dart';

List<TemplateModel> templates = [];

/// Clear all caches for iOS restart to ensure fresh data loading
Future<void> _clearAllCaches() async {
  try {
    printLog('🧹 Clearing all caches for iOS restart...');

    // Clear banner items (already done in your code)
    bannerItems = null;

    // Clear product notifier cache from future_builder.dart
    productsNotifier.value = null;

    // Clear static product cache from StrapiService
    StrapiService.clearProductCache();

    // Clear main categories cache
    mainCategories.clear();
    selectedMainCategory.value = null;

    // Clear promo codes cache
    promoCodesList.clear();

    // Clear all Hive cache boxes
    await CacheBox().box.clear();

    // Clear user data (similar to logout but without navigation)
    UserBox().cleanUpForLogout();

    // Clear local storage data
    // await GetStorageService.clearLocalData();

    // Clear HTTP cache manager
    try {
      await HttpCacheManager().emptyCache();
    } catch (e) {
      printLog('HTTP cache clear failed: $e');
    }

    // Clear any additional static variables that might cache data
    templates.clear();

    // Reset shipping method cost notifier
    ShippingMethodModel.selectedShippingMethodCost.value = 0.0;

    printLog('✅ All caches cleared successfully');
  } catch (e) {
    printLog('❌ Error clearing caches: $e');
  }
}

class OnBoardingTemplateScreen extends HookWidget {
  const OnBoardingTemplateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();
    final currentIndex = useState(0);
    final fontFamily = isEnglish(context)
        ? GoogleFonts.roboto().fontFamily
        : GoogleFonts.cairo().fontFamily;

    void loadTemplates() async {
      final templatesData = (await StrapiService.getSettings()).templates ?? [];
      templates = templatesData;
    }

    useEffect(() {
      if (templates.isEmpty) {
        loadTemplates();
      }

      return () {};
    }, []);

    if (templates.isEmpty) {
      return Scaffold(
        body: Center(
          child: kLoadingWidget(context),
        ),
      );
    }

    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          PageView.builder(
            controller: pageController,
            itemCount: templates.length,
            onPageChanged: (index) {
              currentIndex.value = index;
            },
            itemBuilder: (context, index) {
              return FluxImage(
                imageUrl: templates[index].image?.url ?? '',
                height: context.screenHeight,
                width: context.screenWidth,
                fit: BoxFit.cover,
              ).fadeInFromLeft();
            },
          ),
          Container(
            height: isEnglish(context) ? 300 : 320,
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [Colors.black87, Colors.transparent],
              ),
            ),
            padding: const EdgeInsets.only(top: 50, left: 20, right: 20),
            child: Center(
              child: SizedBox(
                width: Layout.isDisplayDesktop(context)
                    ? context.screenWidth / 2
                    : double.infinity,
                child: Column(
                  crossAxisAlignment: isEnglish(context)
                      ? CrossAxisAlignment.start
                      : CrossAxisAlignment.end,
                  children: [
                    // SmoothPageIndicator with a worm effect
                    SmoothPageIndicator(
                      controller: pageController,
                      count: templates.length,
                      axisDirection: Axis.horizontal,
                      effect: const ExpandingDotsEffect(
                        dotHeight: 12,
                        dotWidth: 12,
                        activeDotColor: Colors.white,
                        dotColor: Color(0xffdbdbdb),
                      ),
                    ),
                    const SizedBox(height: 8),
                    AnimatedTextWidget(
                      currentIndex: currentIndex.value,
                      text: isEnglish(context)
                          ? templates[currentIndex.value].name
                          : templates[currentIndex.value].nameAr,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 46,
                        fontWeight: FontWeight.bold,
                        fontFamily: fontFamily,
                      ),
                    ),

                    const SizedBox(height: 8),
                    Row(
                      children: [
                        if (currentIndex.value != 0) ...[
                          Expanded(
                            flex: 2,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: const BorderSide(color: Colors.white),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 20),
                              ),
                              onPressed: () {
                                pageController.previousPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              child: Text(
                                context.tr.previous,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: fontFamily,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                        if (currentIndex.value != templates.length - 1)
                          Expanded(
                            flex: 3,
                            child: ElevatedButton(
                              onPressed: () {
                                pageController.nextPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 20),
                              ),
                              child: Text(
                                context.tr.next,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: fontFamily,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: const BorderSide(color: Colors.white),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 20, horizontal: 20),
                      ),
                      onPressed: () async {
                        vendorBusinessName =
                            templates[currentIndex.value].name.toLowerCase();

                        await GetStorageService.setData(
                            key: LocalKeys.template, value: vendorBusinessName);

                        final cartModel = CartInject();
                        final _wishlist = ProductWishListModel();

                        cartModel.model.clearCart();
                        await _wishlist.clearWishList();

                        if (kIsWeb) {
                          final newUrl = '/$vendorBusinessName';
                          html.window.history.pushState(null, '', newUrl);
                          html.window.location.reload();

                          // currentVendor =
                          //     await StrapiService.getCurrentVendor();
                          //
                          // setWebFavIcon(currentVendor?.logoUrl);

                          // await Navigator.of(context)
                          //     .pushReplacementNamed(RouteList.dashboard);
                        } else {
                          await _clearAllCaches();

                          await Navigator.of(context).pushNamedAndRemoveUntil(
                            '/', // This will go to the home route which triggers AppInit
                            (route) => false, // Remove all previous routes
                          );

                          // if (UniversalPlatform.isAndroid) {
                          //   await Restart.restartApp();
                          // } else {
                          // iOS: Clear all caches and restart properly

                          // }
                        }
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            context.tr.chooseTemplate,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              fontFamily: fontFamily,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const CircleAvatar(
                            radius: 14,
                            backgroundColor: Colors.white,
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.black,
                              size: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
