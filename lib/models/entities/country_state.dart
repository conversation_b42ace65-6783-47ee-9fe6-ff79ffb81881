import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'country.dart';

class CountryState {
  String? id;
  String? code;
  String? name;
  String? nameAr;
  Country? country;
  List<LatLng> cityBoundaries = [];
  LatLng? cityLocation;

  CountryState? city;

  List<CountryState>? areas;

  bool? freeShipping;

  num? cost;
  bool? isActive;

  CountryState({
    this.id,
    this.code,
    this.name,
    this.country,
    this.city,
    this.nameAr,
    this.cost,
    this.cityBoundaries = const [],
    this.cityLocation,
    this.isActive = true,
    this.areas = const [],
    this.freeShipping,
  });

  CountryState.fromConfig(dynamic parsedJson) {
    if (parsedJson is Map) {
      id = parsedJson['code'];
      code = parsedJson['code'];
      name = parsedJson['name'];
      nameAr = parsedJson['name_ar'];
      freeShipping = parsedJson['free_shipping'] ?? false;
      isActive = parsedJson['is_active'] ?? true;
    }
    if (parsedJson is String) {
      id = parsedJson;
      code = parsedJson;
      name = parsedJson;
      nameAr = parsedJson;
    }
  }

  CountryState.fromStrapiJson(Map parsedJson) {
    id = parsedJson['id'].toString();
    code = parsedJson['code'];
    name = parsedJson['name'];

    if (parsedJson['country'] != null) {
      country = parsedJson['country'].runtimeType == int
          ? Country(id: parsedJson['country'].toString())
          : Country.fromStrapiJson(parsedJson['country']);
    }

    if (parsedJson['boundaries'] != null) {
      cityBoundaries = (parsedJson['boundaries'] as List)
          .map((e) => LatLng(e['lat'], e['lng']))
          .toList();
    }

    if (parsedJson['lat'] != null && parsedJson['lng'] != null) {
      cityLocation = LatLng(double.parse(parsedJson['lat']!.toString()),
          double.parse(parsedJson['lng']!.toString()));
    }

    if (parsedJson['areas'] != null) {
      areas = (parsedJson['areas'] as List)
          .where((e) => e['is_active'] == true)
          .map((e) => CountryState.fromStrapiJson(e))
          .toList();
    }

    cost = parsedJson['cost'] != null
        ? num.tryParse(parsedJson['cost'].toString())
        : null;

    freeShipping = parsedJson['free_shipping'] ?? false;

    isActive = parsedJson['is_active'] ?? true;
  }

  @override
  String toString() {
    return 'CountryState{id: $id, code: $code, name: $name, country: $country, city: $city, cost: $cost, cityBoundaries: $cityBoundaries, cityLocation: $cityLocation}';
  }
}
