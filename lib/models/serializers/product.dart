import 'package:json_annotation/json_annotation.dart';

import '../extra_setting_model.dart';
import 'images.dart';
import 'product_category.dart';

part 'product.g.dart';

//Automatically generated toJson and fromJson methods for product model

//Attributes in SerializerProduct must be matched with return json key,
//otherwise <PERSON><PERSON><PERSON><PERSON>(name: "") must be provided

@JsonSerializable()
class SerializerProduct {
  int? id;
  String? title;
  String? titleAr;
  @Json<PERSON><PERSON>(name: 'is_out_of_stock')
  bool? isOutOfStock;
  List<ExtraSettingsModel> sizes;
  List<ExtraSettingsModel> colors;
  int? inventory;
  @JsonKey(defaultValue: 0)
  double? price;
  @Json<PERSON><PERSON>(name: 'sale_price', defaultValue: 0)
  double? salePrice;
  String? description;
  String? descriptionAr;
  List<Image>? images;
  Thumbnail? thumbnail;
  int? review;
  @JsonKey(name: 'is_sale', defaultValue: false)
  bool? isSale;
  @Json<PERSON>ey(name: 'is_featured')
  bool? isFeatured;
  @Json<PERSON>ey(name: 'inventory_enabled')
  bool inventoryEnabled;
  @Json<PERSON><PERSON>(name: 'is_size_color_inventory')
  bool isSizeColorInventory;
  @JsonKey(name: 'categories')
  List<SerializerProductCategory>? productCategories;
  @JsonKey(name: 'min_quantity_sale_number')
  int? minQuantitySaleNumber;
  @JsonKey(name: 'quantity_sale')
  num? quantitySale;
  @JsonKey(name: 'is_quantity_sale_percentage')
  bool? isQuantitySalePercentage;

  SerializerProduct({
    this.id,
    this.title,
    this.titleAr,
    this.isOutOfStock,
    this.inventory,
    this.price,
    this.salePrice,
    this.images,
    this.thumbnail,
    this.productCategories,
    this.description,
    this.descriptionAr,
    this.isFeatured,
    this.inventoryEnabled = false,
    this.isSizeColorInventory = false,
    this.sizes = const [],
    this.colors = const [],
    this.review,
    this.isSale,
    this.minQuantitySaleNumber,
    this.quantitySale,
    this.isQuantitySalePercentage,
  });

  factory SerializerProduct.fromJson(Map<String, dynamic> json) =>
      _$SerializerProductFromJson(json);

  Map<String, dynamic> toJson() => _$SerializerProductToJson(this);

  @override
  String toString() => 'ProductSerializer { id: $id name: $title}';
}
