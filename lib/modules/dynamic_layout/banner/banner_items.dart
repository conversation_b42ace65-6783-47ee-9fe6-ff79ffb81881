import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:fstore/modules/dynamic_layout/helper/helper.dart';

import '../../../screens/categories/categories_screen.dart';
import '../../../screens/detail/widgets/video_feature.dart';
import '../../../widgets/common/flux_image.dart';
import '../config/box_shadow_config.dart';

class BannerItemWidget extends StatelessWidget {
  // final BannerItemConfig config;
  final (String name, String url) item;
  final double? width;
  final double padding;
  final BoxFit? boxFit;
  final double radius;
  final double? height;
  final Function onTap;
  final bool enableParallax;
  final double parallaxImageRatio;
  final bool isSoundOn,
      autoPlayVideo,
      enableTimeIndicator,
      doubleTapToFullScreen;

  const BannerItemWidget({
    super.key,
    required this.item,
    required this.padding,
    this.width,
    this.boxFit,
    required this.radius,
    this.height,
    required this.onTap,
    this.enableParallax = false,
    this.parallaxImageRatio = 1.2,
    this.isSoundOn = false,
    this.autoPlayVideo = false,
    this.enableTimeIndicator = true,
    this.doubleTapToFullScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    final url = item.$2;
    final isVideo = url.contains('mp4') ||
        url.contains('youtube') ||
        url.contains('vimeo') ||
        url.contains('dailymotion') ||
        url.contains('webm') ||
        url.contains('mov') ||
        url.contains('avi') ||
        url.contains('wmv') ||
        url.contains('flv') ||
        url.contains('mkv') ||
        url.contains('m4p') ||
        url.contains('m4v') ||
        url.contains('mpg') ||
        url.contains('mpeg');

    return isVideo
        ? BannerVideoItem(
            item: item,
            padding: padding,
            isSoundOn: isSoundOn,
            autoPlayVideo: autoPlayVideo,
            enableTimeIndicator: enableTimeIndicator,
            doubleTapToFullScreen: doubleTapToFullScreen,
          )
        : BannerImageItem(
            item: item,
            padding: padding,
            width: width,
            boxFit: boxFit,
            radius: radius,
            height: height,
            onTap: onTap,
            enableParallax: enableParallax,
            parallaxImageRatio: parallaxImageRatio,
          );
  }
}

/// The Banner type to display the image
class BannerImageItem extends StatelessWidget {
  final (String name, String url) item;

  final double? width;
  final double padding;
  final BoxFit? boxFit;
  final double radius;
  final double? height;
  final Function onTap;
  final bool enableParallax;
  final double parallaxImageRatio;
  final BoxShadowConfig? boxShadowConfig;

  const BannerImageItem({
    super.key,
    required this.item,
    required this.padding,
    this.width,
    this.boxFit,
    required this.radius,
    this.height,
    required this.onTap,
    this.enableParallax = false,
    this.parallaxImageRatio = 1.2,
    this.boxShadowConfig,
  });

  @override
  Widget build(BuildContext context) {
    // var paddingVal = padding;
    // var radiusVal = 25.0;

    // final screenSize = MediaQuery.of(context).size;
    // final itemWidth = width ?? screenSize.width;
    // final boxShadow = boxShadowConfig ?? BoxShadowConfig.empty();

    // if (Layout.isDisplayDesktop(context) || Layout.isDisplayTablet(context)) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Positioned.fill(
          child: GestureDetector(
            onTap: () =>
                Navigator.push(context, MaterialPageRoute(builder: (context) {
              return const CategoriesScreen();
            })),
            child: FluxImage(
              imageUrl: item.$2,
              width: context.screenWidth,
              fit: BoxFit.fill,
              // Layout.isDisplayDesktop(context) ? BoxFit.fill : BoxFit.cover,
              // boxFit ?? BoxFit.fitWidth,
              alignment: Alignment.center,
            ),
          ),
        ),
        if (item.$1.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(bottom: 30),
            decoration: const BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 40,
                  spreadRadius: 10,
                  offset: Offset(0, 0),
                ),
              ],
            ),
            child: Text(
              item.$1,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontSize: Layout.isDisplayDesktop(context) ? 60 : 32,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
    // }

    // return GestureDetector(
    //   onTap: () =>
    //       Navigator.push(context, MaterialPageRoute(builder: (context) {
    //     return const CategoriesScreen();
    //   })),
    //   // config.button == null ? onTap(config.jsonData) : null,
    //   child: Container(
    //     width: itemWidth,
    //     height: height,
    //     constraints: const BoxConstraints(minHeight: 10.0),
    //     child: Stack(
    //       children: [
    //         if (enableParallax)
    //           ParallaxImage(
    //             image: item.$2,
    //             // config.image.toString(),
    //             ratio: parallaxImageRatio,
    //           ),
    //         if (!enableParallax)
    //           Container(
    //             margin: EdgeInsets.symmetric(horizontal: paddingVal),
    //             decoration: BoxDecoration(
    //               boxShadow: [
    //                 BoxShadow(
    //                   color: Theme.of(context)
    //                       .colorScheme
    //                       .primary
    //                       .withOpacity(boxShadow.colorOpacity),
    //                   blurRadius: boxShadow.blurRadius,
    //                   spreadRadius: boxShadow.spreadRadius,
    //                   offset: Offset(
    //                     boxShadow.x,
    //                     boxShadow.y,
    //                   ),
    //                 ),
    //               ],
    //             ),
    //             child: FluxImage(
    //               imageUrl: item.$2,
    //               height: 800,
    //               width: context.screenWidth,
    //               fit: BoxFit.fitWidth,
    //
    //               // boxFit ?? BoxFit.fitWidth,
    //               // width: screenSize.width /
    //               //     (Layout.isDisplayDesktop(context) ? 1.25 : 1.1),
    //               // height: Layout.isDisplayDesktop(context) ? null : 220,
    //             ),
    //           ),
    //         // if (item.$1.isNotEmpty)
    //         //   Align(
    //         //     alignment: Alignment.topCenter,
    //         //     child: Text(
    //         //       item.$1,
    //         //       style: Theme.of(context).textTheme.bodyLarge?.copyWith(
    //         //         // color: HexColor(config.description?.color),
    //         //         fontSize: 16,
    //         //         fontFamily: 'Roboto',
    //         //         shadows: <Shadow>[
    //         //           // if (config.description?.enableShadow ?? false)
    //         //           const Shadow(
    //         //             offset: Offset(2.0, 2.0),
    //         //             blurRadius: 3.0,
    //         //             color: Colors.black,
    //         //           ),
    //         //         ],
    //         //       ),
    //         //     ),
    //         //   ),
    //         if (item.$1.isNotEmpty)
    //           Align(
    //             alignment: Alignment.center,
    //             child: Text(
    //               item.$1,
    //               style: Theme.of(context).textTheme.bodyLarge?.copyWith(
    //                 color: Colors.white,
    //                 fontSize: 26,
    //                 fontFamily: 'Roboto',
    //                 fontWeight: FontWeight.bold,
    //                 shadows: <Shadow>[
    //                   // if (config.title?.enableShadow ?? false)
    //                   const Shadow(
    //                     offset: Offset(2.0, 2.0),
    //                     blurRadius: 3.0,
    //                     color: Colors.black,
    //                   ),
    //                 ],
    //               ),
    //             ),
    //           ),
    //         // if (config.button != null)
    //         //   Align(
    //         //     alignment: config.button?.alignment ?? Alignment.topCenter,
    //         //     child: InkWell(
    //         //       onTap: () => onTap(config.jsonData),
    //         //       child: Container(
    //         //         padding: const EdgeInsets.symmetric(
    //         //             horizontal: 15, vertical: 10),
    //         //         decoration: BoxDecoration(
    //         //             color: HexColor(config.button?.backgroundColor),
    //         //             borderRadius: BorderRadius.circular(5)),
    //         //         child: Text(
    //         //           config.button?.text ?? '',
    //         //           style: Theme.of(context).textTheme.bodySmall?.copyWith(
    //         //               color: HexColor(config.button?.textColor),
    //         //               fontSize: 16),
    //         //         ),
    //         //       ),
    //         //     ),
    //         //   ),
    //       ],
    //     ),
    //   ),
    // );
  }
}

class BannerVideoItem extends StatelessWidget {
  final (String name, String url) item;

  final bool isSoundOn,
      autoPlayVideo,
      enableTimeIndicator,
      doubleTapToFullScreen;
  final double padding;

  const BannerVideoItem({
    super.key,
    required this.item,
    this.isSoundOn = false,
    this.autoPlayVideo = false,
    this.enableTimeIndicator = true,
    this.doubleTapToFullScreen = false,
    required this.padding,
  });

  @override
  Widget build(BuildContext context) {
    // var paddingVal = config.padding ?? padding;

    return Padding(
      padding: EdgeInsets.only(left: padding, right: padding),
      child: FeatureVideoPlayer(
        item.$2,
        autoPlay: autoPlayVideo,
        isSoundOn: isSoundOn,
        enableTimeIndicator: enableTimeIndicator,
        aspectRatio: 16 / 9,
        doubleTapToFullScreen: doubleTapToFullScreen,
        showFullScreenButton: true,
        showVolumeButton: true,
      ),
    );
  }
}

//class BannerItemWidget extends StatelessWidget {
//   final BannerItemConfig config;
//   final double? width;
//   final double padding;
//   final BoxFit? boxFit;
//   final double radius;
//   final double? height;
//   final Function onTap;
//   final bool enableParallax;
//   final double parallaxImageRatio;
//   final bool isSoundOn,
//       autoPlayVideo,
//       enableTimeIndicator,
//       doubleTapToFullScreen;
//
//   const BannerItemWidget({
//     super.key,
//     required this.config,
//     required this.padding,
//     this.width,
//     this.boxFit,
//     required this.radius,
//     this.height,
//     required this.onTap,
//     this.enableParallax = false,
//     this.parallaxImageRatio = 1.2,
//     this.isSoundOn = false,
//     this.autoPlayVideo = false,
//     this.enableTimeIndicator = true,
//     this.doubleTapToFullScreen = false,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return
//       (config.video?.isNotEmpty ?? false)
//         ? BannerVideoItem(
//             config: config,
//             padding: padding,
//             isSoundOn: isSoundOn,
//             autoPlayVideo: autoPlayVideo,
//             enableTimeIndicator: enableTimeIndicator,
//             doubleTapToFullScreen: doubleTapToFullScreen,
//           )
//         :
//       BannerImageItem(
//             config: config,
//             padding: padding,
//             width: width,
//             boxFit: boxFit,
//             radius: radius,
//             height: height,
//             onTap: onTap,
//             enableParallax: enableParallax,
//             parallaxImageRatio: parallaxImageRatio,
//           );
//   }
// }
//
// /// The Banner type to display the image
// class BannerImageItem extends StatelessWidget {
//   final BannerItemConfig config;
//   final double? width;
//   final double padding;
//   final BoxFit? boxFit;
//   final double radius;
//   final double? height;
//   final Function onTap;
//   final bool enableParallax;
//   final double parallaxImageRatio;
//   final BoxShadowConfig? boxShadowConfig;
//
//   const BannerImageItem({
//     super.key,
//     required this.config,
//     required this.padding,
//     this.width,
//     this.boxFit,
//     required this.radius,
//     this.height,
//     required this.onTap,
//     this.enableParallax = false,
//     this.parallaxImageRatio = 1.2,
//     this.boxShadowConfig,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     var paddingVal = config.padding ?? padding;
//     var radiusVal = config.radius ?? radius;
//
//     final screenSize = MediaQuery.of(context).size;
//     final itemWidth = width ?? screenSize.width;
//     final boxShadow = boxShadowConfig ?? BoxShadowConfig.empty();
//
//     return GestureDetector(
//       onTap: () => config.button == null ? onTap(config.jsonData) : null,
//       child: Container(
//         width: itemWidth,
//         height: height,
//         constraints: const BoxConstraints(minHeight: 10.0),
//         child: Stack(
//           children: [
//             if (enableParallax)
//               ParallaxImage(
//                 image: config.image.toString(),
//                 ratio: parallaxImageRatio,
//               ),
//             if (!enableParallax)
//               Container(
//                 margin: EdgeInsets.symmetric(horizontal: paddingVal),
//                 decoration: BoxDecoration(
//                   boxShadow: [
//                     BoxShadow(
//                       color: Theme.of(context)
//                           .colorScheme
//                           .primary
//                           .withOpacity(boxShadow.colorOpacity),
//                       blurRadius: boxShadow.blurRadius,
//                       spreadRadius: boxShadow.spreadRadius,
//                       offset: Offset(
//                         boxShadow.x,
//                         boxShadow.y,
//                       ),
//                     ),
//                   ],
//                 ),
//                 child: ClipRRect(
//                   borderRadius: BorderRadius.circular(radiusVal),
//                   child: FluxImage(
//                     imageUrl: config.image,
//                     fit: boxFit ?? BoxFit.fitWidth,
//                     width: itemWidth,
//                     height: height,
//                   ),
//                 ),
//               ),
//             if (config.description != null)
//               Align(
//                 alignment: config.description?.alignment ?? Alignment.topCenter,
//                 child: Text(
//                   config.description?.text ?? '',
//                   style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                     color: HexColor(config.description?.color),
//                     fontSize: config.description?.fontSize,
//                     fontFamily: config.description?.fontFamily,
//                     shadows: <Shadow>[
//                       if (config.description?.enableShadow ?? false)
//                         const Shadow(
//                           offset: Offset(2.0, 2.0),
//                           blurRadius: 3.0,
//                           color: Colors.black,
//                         ),
//                     ],
//                   ),
//                 ),
//               ),
//             if (config.title != null)
//               Align(
//                 alignment: config.title?.alignment ?? Alignment.topCenter,
//                 child: Text(
//                   config.title?.text ?? '',
//                   style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                     color: HexColor(config.title?.color),
//                     fontSize: config.title?.fontSize,
//                     fontFamily: config.title?.fontFamily,
//                     fontWeight: FontWeight.bold,
//                     shadows: <Shadow>[
//                       if (config.title?.enableShadow ?? false)
//                         const Shadow(
//                           offset: Offset(2.0, 2.0),
//                           blurRadius: 3.0,
//                           color: Colors.black,
//                         ),
//                     ],
//                   ),
//                 ),
//               ),
//             if (config.button != null)
//               Align(
//                 alignment: config.button?.alignment ?? Alignment.topCenter,
//                 child: InkWell(
//                   onTap: () => onTap(config.jsonData),
//                   child: Container(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: 15, vertical: 10),
//                     decoration: BoxDecoration(
//                         color: HexColor(config.button?.backgroundColor),
//                         borderRadius: BorderRadius.circular(5)),
//                     child: Text(
//                       config.button?.text ?? '',
//                       style: Theme.of(context).textTheme.bodySmall?.copyWith(
//                           color: HexColor(config.button?.textColor),
//                           fontSize: 16),
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class BannerVideoItem extends StatelessWidget {
//   final BannerItemConfig config;
//   final bool isSoundOn,
//       autoPlayVideo,
//       enableTimeIndicator,
//       doubleTapToFullScreen;
//   final double padding;
//
//   const BannerVideoItem({
//     super.key,
//     required this.config,
//     this.isSoundOn = false,
//     this.autoPlayVideo = false,
//     this.enableTimeIndicator = true,
//     this.doubleTapToFullScreen = false,
//     required this.padding,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     var paddingVal = config.padding ?? padding;
//
//     return Padding(
//       padding: EdgeInsets.only(left: paddingVal, right: paddingVal),
//       child: FeatureVideoPlayer(
//         config.video ?? '',
//         autoPlay: autoPlayVideo,
//         isSoundOn: isSoundOn,
//         enableTimeIndicator: enableTimeIndicator,
//         aspectRatio: 16 / 9,
//         doubleTapToFullScreen: doubleTapToFullScreen,
//         showFullScreenButton: true,
//         showVolumeButton: true,
//       ),
//     );
//   }
// }
