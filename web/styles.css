/* Landing Page Styles */
body, html {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: '<PERSON>o', 'Arial', sans-serif;
  background: linear-gradient(135deg, #00c851 0%, #007e33 50%, #004d20 100%);
  overflow: hidden;
}

#landing-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #00c851 0%, #007e33 50%, #004d20 100%);
  color: white;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.landing-content {
  text-align: center;
  max-width: 400px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.app-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Animated Text Logo Styles */
/* Animated Text Logo Styles */
.animated-text-logo {
  font-size: 3rem;
  font-weight: 700;
  margin-top: 10px;
  margin-bottom: 10px; /* reduced from 40px */
  background: linear-gradient(45deg, #ffffff, #e3f2fd, #ffffff, #e3f2fd);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  letter-spacing: 2px;
  text-transform: uppercase;
  opacity: 0;
  transform: scale(0.5) translateY(50px);
  animation:
    textLogoAppear 1.5s ease-out 0.3s forwards,
    textShimmer 3s ease-in-out infinite,
    textFloat 4s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes textLogoAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(30px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0px);
  }
}

@keyframes textShimmer {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes textFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  25% { transform: translateY(-8px) scale(1.02); }
  50% { transform: translateY(-5px) scale(1); }
  75% { transform: translateY(-12px) scale(0.98); }
}

.app-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 10px;
  opacity: 0;
  animation: fadeInUp 1s ease-out 0.5s forwards;
}

.app-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  margin-bottom: 40px;
  opacity: 0.9;
  opacity: 0;
  animation: fadeInUp 1s ease-out 0.7s forwards;
}

#loading-status {
  font-size: 1rem;
  margin-bottom: 20px;
  opacity: 0.8;
  min-height: 24px;
  opacity: 0;
  animation: fadeInUp 1s ease-out 0.9s forwards;
}

.loader-container {
  position: relative;
  margin: 20px auto;
  display: none; /* Hide circular loader */
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-bar {
  width: 200px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin: 20px auto;
  overflow: hidden;
  opacity: 0;
  animation: fadeInUp 1s ease-out 1.3s forwards;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
  border-radius: 2px;
  animation: progressFill 4s ease-in-out infinite;
}

@keyframes progressFill {
  0% { width: 0%; }
  25% { width: 30%; }
  50% { width: 60%; }
  75% { width: 85%; }
  100% { width: 100%; }
}

.loading-dots {
  display: inline;
  margin-left: 0;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Responsive design */
@media (max-width: 768px) {
  .app-title {
    font-size: 2rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .app-logo {
    width: 100px;
    height: 100px;
  }

  .animated-text-logo {
    font-size: 2.2rem;
    letter-spacing: 1px;
    min-height: 100px;
  }

  .landing-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.8rem;
  }

  .animated-text-logo {
    font-size: 1.8rem;
    letter-spacing: 0.5px;
    min-height: 80px;
  }

  .progress-bar {
    width: 150px;
  }
}

/* Hide Flutter loading indicator */
.loading,
flutter-view .loading {
  display: none !important;
}

/* Ensure Flutter app is hidden initially until fully ready */
flt-glass-pane,
flutter-view,
[flt-renderer],
flt-scene-host,
canvas[flt-renderer] {
  opacity: 0 !important;
  transition: opacity 0.8s ease-in;
}

flt-glass-pane.flutter-ready,
flutter-view.flutter-ready,
[flt-renderer].flutter-ready,
flt-scene-host.flutter-ready,
canvas[flt-renderer].flutter-ready {
  opacity: 1 !important;
}

/* Hide any Flutter debug overlays during loading */
flt-semantics-host {
  opacity: 0;
  transition: opacity 0.8s ease-in;
}

flt-semantics-host.flutter-ready {
  opacity: 1;
}

/* Ensure smooth transition */
body.flutter-loading {
  overflow: hidden;
}

body.flutter-ready {
  overflow: auto;
}
