name: fstore
publish_to: "none"
description: <PERSON><PERSON><PERSON>pp.

version: 1.0.22+122
scripts:
  run: flutter run
  pub_get: flutter pub get

  build_ios: flutter build ios --release
  build_android: flutter build appbundle --release
  build_apk: flutter build apk --release

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  inspireui:
    path: packages/inspireui/
  cupertino_icons: ^1.0.2

  flux_firebase:
    path: packages/flux_firebase/

  # Base
  flutter_localization: ^0.2.0
  intl: ^0.20.0
  crypto: 3.0.3
  path_provider: ^2.1.5
#  path_provider: 2.1.2
  flutter_secure_storage: ^9.2.4
  hive: 2.2.3
  hive_flutter: 1.1.0
  http: ^1.1.0
  dio: ^5.8.0+1
  http_auth: 1.0.4
  collection: any
  quiver: 3.2.1
  async: 2.11.0
  html: 0.15.4
  vector_math: 2.1.4
  path: ^1.9.1
  flutter_svg: ^2.0.17
  notification_permissions:
    path: packages/notification_permissions
#  flutter_local_notifications: ^18.0.1
#  onesignal_flutter: ^5.2.7
  new_version_plus: 0.0.11

  # STATE MANAGEMENT
  provider: ^6.1.2
  get_it: 7.6.7

  # SPLASH SCREEN, ONBOARD
  lottie: 3.1.0
#  rive:
#    path: packages/rive

  # WEB TOOLS
  html_unescape: 2.0.0
  webview_flutter:
  responsive_builder: 0.7.0
  webview_flutter_web: ^0.2.3+1
  flutter_inappwebview: ^6.1.3
  carousel_slider: ^5.0.0


  # PAYMENT
  paypal_payment: ^1.0.0
  flutter_direct_caller_plugin: ^0.0.4

  #  razorpay_flutter:
  #    git:
  #      url: https://github.com/inspireui/razorpay-flutter
  #      ref: b5b7f1497e9581aadb60cdd00824e8a5747bb03a

  # HTML render
  flutter_widget_from_html_core: ^0.16.0
  fwfh_svg: ^0.16.0
  fwfh_cached_network_image: ^0.16.0
  #  fwfh_url_launcher: ^0.9.1
  #  fwfh_chewie: 0.7.1+2 # accentColor issue
  fwfh_webview: ^0.15.4

  # VIDEO
  video_player: ^2.9.3
#  youtube_player_iframe: ^5.2.1
#  youtube_player_iframe:
#    git:
#      url: https://github.com/inspireui/youtube_player_flutter.git
#      path: packages/youtube_player_iframe
#      ref: c2268281ca7f1f051f7ccab373e982d22c540910

  # MAP
  google_maps_flutter: ^2.9.0
  location: 5.0.3
  #  geocoding: 2.2.0

  # AUTHENTICATION
  local_auth: ^2.3.0
  local_auth_android: ^1.0.48
  the_apple_sign_in: 1.1.1
  flutter_facebook_auth: 6.1.1
  google_sign_in: 6.2.1
  sms_autofill: 2.3.1
  permission_handler: ^11.4.0

  # FILES, IMAGES
  cached_network_image: 3.3.1
  transparent_image: 2.0.1
  image_picker: ^1.1.2
#  flutter_native_image: 0.0.6+1
  flutter_cache_manager: 3.3.1
  wechat_assets_picker: ^9.3.0

  quickalert:
    path: packages/quick_alert

  # TOOLS
  google_fonts: 6.1.0
  random_string: 2.3.1
  json_annotation: ^4.9.0
  freezed_annotation: 2.4.1
  timeago: ^3.7.0
  universal_platform: ^1.1.0
  uuid: 4.3.3
  easy_debounce: 2.0.3
  devicelocale: 0.7.0
  visibility_detector: 0.4.0+2
  rate_my_app: 2.0.0
  flutter_linkify: 6.0.0
  gms_check:
    path: packages/gms_check
  google_mobile_ads: ^5.3.1
  app_tracking_transparency: 2.0.4
  translator: ^1.0.3+1

  # UI
  flutter_spinkit: 5.2.0
  smooth_page_indicator: 1.1.0
  animated_text_kit: 4.2.2
  flash: 3.1.0
  flutter_staggered_grid_view: 0.7.0
  pin_code_fields: 8.0.1
  country_pickers: ^3.0.1
  intro_slider: 4.2.1
  extended_image: ^8.3.0
  dotted_decoration: 2.0.0
  qr_code_scanner_plus: ^2.0.10+1
  sticky_headers: 0.3.0+2
  intrinsic_grid_view: ^0.0.2
  jumping_dot: 0.0.6
  readmore: 2.2.0
  flutter_animate: 4.5.0
  flutter_async_autocomplete: 0.1.1
  scrollable_positioned_list: 0.3.8
  country_code_picker: 3.0.0
  rect_getter: 1.1.0
  scroll_to_index: 3.0.1
  flutter_zoom_drawer: 3.2.0
  infinite_carousel: 1.1.1
  timezone: 0.9.2
#  flutter_phone_direct_caller: ^2.1.1
  flutter_hooks:
  universal_html: ^2.2.4



  #  googleapis:
  googleapis_auth:



  ### Flutter 3 fix warning
  intl_phone_number_input: # custom feature for phone number
    git:
      url: https://github.com/inspireui/intl_phone_number_input
      ref: 1a3fa368a009eac3cf02a91052956981da10e725
  flutter_swiper_null_safety:
    git:
      url: https://github.com/inspireui/flutter_swiper_null_safety
      ref: 18e8d2d642ff9e0013fc4477c6d64c52732b463a
#  flare_flutter:
#    git:
#      url: https://github.com/inspireui/Flare-Flutter.git
#      ref: fd4bcba22aae4c028286e453deeb78f3311e689a
#      path: flare_flutter
  pull_to_refresh:
    git:
      url: https://github.com/inspireui/flutter_pull_to_refresh
      ref: 24df296e282cd8b4ea8d3a5e171f4156a0295d1b
  multiple_localization:
    git:
      url: https://github.com/inspireui/flutter_multiple_localization
      ref: 48b65fa88ed468331d56ee86a102d808596c0a50

  ###---- Some extra feature is disable by default -----###
  ### 💳 Facebook Ads
  ### Search "Enable Facebook Ads" & uncomment to use  - https://tppr.me/9Pkf9
  # facebook_audience_network: 1.0.1

  ### ⬇️ Enable In App Update on Android feature
  ### Uncomment file lib/common/tools/in_app_update_for_android.dart
  in_app_update: 4.2.2
  meta_seo: 3.0.9
  url_launcher_ios: 6.3.1

  restart_app: ^1.3.2

  get_storage: ^2.1.1

  font_awesome_flutter: ^10.8.0
  flutter_custom_clippers: ^2.1.0

dependency_overrides:
  url_launcher_ios: 6.3.1
  url_launcher_android: ^6.3.15
  web: ^0.5.1
  archive: ^3.6.1
  win32: ^5.5.4
  timeago: ^3.7.0
  intl: ^0.20.0
  async: ^2.12.0
  dio_web_adapter: ^2.1.0
  inspireui:
    path: packages/inspireui/
  flutter_plugin_android_lifecycle: ^2.0.27
  webview_flutter_android: ^4.3.3
  google_sign_in_android: ^6.2.0
  photo_manager:
    path: packages/photo_manager
  shared_preferences: ^2.5.2
  sms_autofill: ^2.4.1
  sqflite: ^2.4.2
  video_player_android: ^2.8.2

dev_dependencies:
  flutter_lints: 3.0.1
  build_runner: 2.4.8
  freezed: 2.4.7
  json_serializable: ^6.9.0

  delete_un_used_assets: ^1.0.1

  ### Enable to use Flutter Test Driver
  #  flutter_driver:
  #    sdk: flutter
  #  test: 1.16.5
  #  dependency_validator: 3.2.2  # pub run dependency_validator

  ### Enable Generate Icon library
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.3.9

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_background: "assets/images/app_icon.png"
  adaptive_icon_foreground: "assets/images/app_icon.png"
  adaptive_icon_foreground_inset: 16

# ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/app_icon.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/app_icon.png'

flutter:
  uses-material-design: true
  assets:
    - lib/config/
    - lib/config/states/
    - assets/icons/tabs/
    - assets/icons/payment/
    - assets/images/
    - assets/animated/
    - assets/images/country/
    - assets/map_styles/
    - assets/html/
    - google_fonts/
    - shorebird.yaml

### Uncomment to use custom font
#  fonts:
#    - family: Your Custom Font
#      fonts:
#      - asset: google_fonts/GE-Hili-Book.ttf

flutter_intl:
  enabled: true
  use_deferred_loading: true
