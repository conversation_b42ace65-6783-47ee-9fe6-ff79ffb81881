buildscript {
    repositories {
        // ...
        maven { url 'https://plugins.gradle.org/m2/' } // Gradle Plugin Portal
    }
    dependencies {
        // ...
    }
}

plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def envProperties = new Properties()
def envPropsFile = rootProject.file('../configs/env.props')
def envPropertiesFile = rootProject.file('../configs/env.properties')

if (envPropertiesFile.exists() && envPropsFile.exists()) {
    println "====================================================================="
    println "⚠️  Warning: env.properties is deprecated, please rename to env.props"
    println "====================================================================="
}

if (envPropertiesFile.exists() && !envPropsFile.exists()) {
    println "================================================================="
    println "⚠️  Warning: env.properties is deprecated and should not be used"
    println "🪄️  env.properties has been renamed to env.props automatically"
    println "================================================================="

    ant.move file: '../../configs/env.properties', tofile: '../../configs/env.props'
}

envPropsFile = rootProject.file('../configs/env.props')
envPropertiesFile = rootProject.file('../configs/env.properties')

if (envPropsFile.exists()) {
    println "🔧 Loading configs from configs/env.props...\n"
    envPropsFile.withReader('UTF-8') { reader ->
        envProperties.load(reader)
    }
} else {
    if (envPropertiesFile.exists()) {
        println "🔧 Loading configs from configs/env.properties...\n"
        envPropertiesFile.withReader('UTF-8') { reader ->
            envProperties.load(reader)
        }
    }
}


android {
    compileSdkVersion 35
    namespace 'com.idea2App.pernike'

    ndkVersion "28.0.13004108"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    tasks.withType(JavaCompile).configureEach {
        options.warnings = false
    }
    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId envProperties.getProperty('androidPackageName', '')
        minSdkVersion 23
        targetSdkVersion 34

        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true

        // // Zoho SalesIQ Mobilisten
        // Comment `resConfigs "en"` to fix Zoho SaleIQ not support
        // multi-language on Android.
        // Un-comment to reduce build size.
        resConfigs "en"

        // Using `manifestPlaceholders` will replace the param on
        // `AndroidManifest.xml` file with the correct value.
        // For example: <meta-data android:name="default-url"
        // android:value="${websiteUrl}" />
        // will be <meta-data android:name="default-url" android:value="inspireui.com" />
        manifestPlaceholders += [
                envatoPurchaseCode           : envProperties.getProperty('envatoPurchaseCode', ''),
                websiteUrl                   : envProperties.getProperty('websiteUrl', ''),
                websiteDomain                : envProperties.getProperty('websiteDomain', ''),
                customScheme                 : envProperties.getProperty('customScheme', ''),
                googleApiKeyAndroid          : envProperties.getProperty('googleApiKeyAndroid', ''),
                adMobAppIdAndroid            : envProperties.getProperty('adMobAppIdAndroid', ''),
                facebookClientToken          : envProperties.getProperty('facebookClientToken', ''),
                facebookLoginProtocolScheme  : envProperties.getProperty('facebookLoginProtocolScheme', ''),
                branchKeyLive                : envProperties.getProperty('branchKeyLive', ''),
                branchKeyTest                : envProperties.getProperty('branchKeyTest', ''),
                branchLiveLinkDomain         : envProperties.getProperty('branchLiveLinkDomain', ''),
                branchLiveAlternateLinkDomain: envProperties.getProperty('branchLiveAlternateLinkDomain', ''),
                branchTestLinkDomain         : envProperties.getProperty('branchTestLinkDomain', ''),
                branchTestAlternateLinkDomain: envProperties.getProperty('branchTestAlternateLinkDomain', ''),
                branchTestMode               : envProperties.getProperty('branchTestMode', ''),
        ]

        // If you use `resValue`, it will generate a resource of the type you
        // specify into your app's res directory.
        resValue 'string', 'facebookAppId', envProperties.getProperty('facebookAppId', '')

        // Not sure why we need to use `appName` as resValue.
        resValue 'string', 'app_name', envProperties.getProperty('appName', '')

        // Notification color for Firebase
        resValue 'color', 'notification_color', '#' + envProperties.getProperty('notificationColor', 'FF2EB0FE')

        // Notification color for OneSignal
        resValue 'string', 'onesignal_notification_accent_color', envProperties.getProperty('notificationColor', 'FF2EB0FE')
    }

    signingConfigs {
        release {
            keyAlias envProperties.getProperty('keyAlias', '')
            keyPassword envProperties.getProperty('keyPassword', '')
            storeFile rootProject.file('../configs/' + envProperties.getProperty('storeFile', ''))
            storePassword envProperties.getProperty('storePassword', '')
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'

    implementation 'com.google.android.material:material:1.6.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'androidx.browser:browser:1.3.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0'
}

googleServices { disableVersionCheck = true }

//tasks.register('copyGoogleServices', Copy) {
//    from '../../configs'
//    include 'google-services.json'
//    into '.'
//}

def notificationIcon = envProperties.getProperty('notificationIcon', '')
tasks.register('copyNotificationIcon', Copy) {
    from '../../configs/'
    include notificationIcon
    into './src/main/res/drawable/'
    rename { String fileName ->
        fileName.replace(notificationIcon, 'ic_stat_onesignal_default.png')
    }
    doLast {
        def originalFile = rootProject.file('./src/main/res/drawable/' + notificationIcon)
        if (originalFile.exists()) {
            originalFile.delete()
        }
    }
}

//tasks.register('copyConfigFiles', Copy) {
//    from '../../configs/customized/'
//    include '**'
//    into '../../'
//}

def notificationIconFile = rootProject.file('../configs/' + notificationIcon)
if (notificationIcon && notificationIconFile.exists()) {
    println "🔧 Copying configs/" + notificationIcon + " to android/app/src/main/res/drawable/ic_stat_onesignal_default.png"
    preBuild.dependsOn(copyNotificationIcon)
}

println "🔧 Copying configs/google-services.json to android/app/google-services.json"
//preBuild.dependsOn(copyGoogleServices)

println "🔧 Copying configs/customized to project..."
//preBuild.dependsOn(copyConfigFiles)

println "\n🪄  Building " + envProperties['appName'] + "... 🪄\n"
println "\n🔑 Signing with keystore " + envProperties['storeFile'] + "... 🔑\n"
